"""
Django forms for filter management with validation
"""

from django import forms
from django.core.exceptions import ValidationError
import json

from .models import FilterGroup, FilterDefinition, LocationHierarchy, PlatformType, OptionForType, FilterType


class FilterGroupForm(forms.ModelForm):
    """Form for creating and editing filter groups"""
    
    class Meta:
        model = FilterGroup
        fields = ['name', 'option_for', 'channel', 'sort_order', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter filter group name'
            }),
            'option_for': forms.Select(attrs={'class': 'form-control'}),
            'channel': forms.Select(attrs={'class': 'form-control'}),
            'sort_order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'value': 0
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].help_text = 'Unique name for the filter group (e.g., "Demography & Identity")'
        self.fields['option_for'].help_text = 'Target audience for this filter group'
        self.fields['channel'].help_text = 'Platform this filter group applies to'
        self.fields['sort_order'].help_text = 'Order in which this group appears (lower numbers first)'
        self.fields['is_active'].help_text = 'Whether this filter group is currently active'
    
    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name.strip()) < 3:
            raise ValidationError('Filter group name must be at least 3 characters long.')
        return name.strip()


class FilterDefinitionForm(forms.ModelForm):
    """Form for creating and editing filter definitions"""
    
    options_json = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 6,
            'placeholder': 'Enter filter options as JSON array (optional)\nExample: [{"label": "Male", "value": "male", "description": "Male creators"}]'
        }),
        required=False,
        help_text='JSON array of filter options. Leave empty if not applicable.'
    )
    
    class Meta:
        model = FilterDefinition
        fields = [
            'group', 'name', 'filter_type', 'icon', 'has_minmax', 'has_enter_value', 
            'has_search_box', 'placeholder', 'db_field', 'api_field', 'sort_order', 'is_active'
        ]
        widgets = {
            'group': forms.Select(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter filter name'
            }),
            'filter_type': forms.Select(attrs={'class': 'form-control'}),
            'icon': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Icon class or name (optional)'
            }),
            'has_minmax': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'has_enter_value': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'has_search_box': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'placeholder': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Placeholder text for the filter'
            }),
            'db_field': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Database field name (optional)'
            }),
            'api_field': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'API parameter name (optional)'
            }),
            'sort_order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'value': 0
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set help text
        self.fields['group'].help_text = 'Filter group this filter belongs to'
        self.fields['name'].help_text = 'Unique name for the filter within its group'
        self.fields['filter_type'].help_text = 'Type of UI component for this filter'
        self.fields['icon'].help_text = 'CSS class or icon name for display'
        self.fields['has_minmax'].help_text = 'Whether this filter supports min/max values'
        self.fields['has_enter_value'].help_text = 'Whether users can enter custom values'
        self.fields['has_search_box'].help_text = 'Whether to show a search box (required for multilevel-checkbox)'
        self.fields['placeholder'].help_text = 'Placeholder text shown in the filter UI'
        self.fields['db_field'].help_text = 'Database column name this filter maps to'
        self.fields['api_field'].help_text = 'API parameter name for this filter'
        self.fields['sort_order'].help_text = 'Order within the filter group (lower numbers first)'
        self.fields['is_active'].help_text = 'Whether this filter is currently active'
        
        # Populate options_json field if editing existing filter
        if self.instance and self.instance.pk and self.instance.options:
            self.fields['options_json'].initial = json.dumps(self.instance.options, indent=2)
    
    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name.strip()) < 2:
            raise ValidationError('Filter name must be at least 2 characters long.')
        return name.strip()
    
    def clean_options_json(self):
        options_json = self.cleaned_data.get('options_json', '').strip()
        
        if not options_json:
            return []
        
        try:
            options = json.loads(options_json)
            if not isinstance(options, list):
                raise ValidationError('Options must be a JSON array.')
            
            # Validate each option
            for i, option in enumerate(options):
                if not isinstance(option, dict):
                    raise ValidationError(f'Option {i+1} must be a JSON object.')
                
                if 'label' not in option or 'value' not in option:
                    raise ValidationError(f'Option {i+1} must have "label" and "value" fields.')
                
                if not isinstance(option['label'], str) or not option['label'].strip():
                    raise ValidationError(f'Option {i+1} label must be a non-empty string.')
                
                if not isinstance(option['value'], str) or not option['value'].strip():
                    raise ValidationError(f'Option {i+1} value must be a non-empty string.')
            
            return options
            
        except json.JSONDecodeError as e:
            raise ValidationError(f'Invalid JSON format: {str(e)}')
    
    def clean(self):
        cleaned_data = super().clean()
        filter_type = cleaned_data.get('filter_type')
        has_search_box = cleaned_data.get('has_search_box')
        
        # Validate multilevel-checkbox requirements
        if filter_type == FilterType.MULTILEVEL_CHECKBOX and not has_search_box:
            raise ValidationError('Multilevel checkbox filters must have search box enabled.')
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Set options from JSON field
        options_json = self.cleaned_data.get('options_json')
        if options_json:
            instance.options = options_json
        else:
            instance.options = []
        
        if commit:
            instance.save()
        
        return instance


class LocationHierarchyForm(forms.ModelForm):
    """Form for creating and editing location hierarchy"""
    
    class Meta:
        model = LocationHierarchy
        fields = ['name', 'code', 'level', 'parent', 'population', 'tier', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter location name'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Location code (e.g., US, CA)'
            }),
            'level': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 5
            }),
            'parent': forms.Select(attrs={'class': 'form-control'}),
            'population': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0
            }),
            'tier': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Tier (e.g., tier1, tier2, rural)'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set help text
        self.fields['name'].help_text = 'Name of the location'
        self.fields['code'].help_text = 'Short code for the location (ISO codes recommended)'
        self.fields['level'].help_text = 'Hierarchy level (0=Country, 1=State/Region, 2=City, etc.)'
        self.fields['parent'].help_text = 'Parent location (leave empty for top-level)'
        self.fields['population'].help_text = 'Population count (optional)'
        self.fields['tier'].help_text = 'Location tier classification (optional)'
        self.fields['is_active'].help_text = 'Whether this location is currently active'
        
        # Filter parent choices based on level
        if 'level' in self.data:
            try:
                level = int(self.data.get('level'))
                if level > 0:
                    self.fields['parent'].queryset = LocationHierarchy.objects.filter(
                        level=level-1, is_active=True
                    ).order_by('name')
                else:
                    self.fields['parent'].queryset = LocationHierarchy.objects.none()
            except (ValueError, TypeError):
                pass
        elif self.instance.pk and self.instance.level > 0:
            self.fields['parent'].queryset = LocationHierarchy.objects.filter(
                level=self.instance.level-1, is_active=True
            ).order_by('name')
        else:
            self.fields['parent'].queryset = LocationHierarchy.objects.none()
    
    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name.strip()) < 2:
            raise ValidationError('Location name must be at least 2 characters long.')
        return name.strip()
    
    def clean(self):
        cleaned_data = super().clean()
        level = cleaned_data.get('level')
        parent = cleaned_data.get('parent')
        
        # Validate parent-level relationship
        if level == 0 and parent:
            raise ValidationError('Top-level locations (level 0) cannot have a parent.')
        
        if level > 0 and not parent:
            raise ValidationError('Locations with level > 0 must have a parent.')
        
        if parent and parent.level != level - 1:
            raise ValidationError(f'Parent must be at level {level - 1}.')
        
        return cleaned_data


class BulkActionForm(forms.Form):
    """Form for bulk actions on filters"""
    
    ACTION_CHOICES = [
        ('enable', 'Enable Selected'),
        ('disable', 'Disable Selected'),
        ('delete', 'Delete Selected'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    selected_items = forms.CharField(
        widget=forms.HiddenInput(),
        help_text='Comma-separated list of item IDs'
    )
    
    def clean_selected_items(self):
        selected_items = self.cleaned_data['selected_items']
        if not selected_items:
            raise ValidationError('No items selected.')
        
        try:
            item_ids = [item.strip() for item in selected_items.split(',') if item.strip()]
            if not item_ids:
                raise ValidationError('No valid items selected.')
            return item_ids
        except Exception:
            raise ValidationError('Invalid item selection format.')


class FilterSearchForm(forms.Form):
    """Form for searching and filtering"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name...'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All'), ('active', 'Active'), ('inactive', 'Inactive')],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, *args, **kwargs):
        model_type = kwargs.pop('model_type', None)
        super().__init__(*args, **kwargs)
        
        if model_type == 'filter_group':
            self.fields['channel'] = forms.ChoiceField(
                required=False,
                choices=[('', 'All Platforms')] + list(PlatformType.choices),
                widget=forms.Select(attrs={'class': 'form-control'})
            )
            self.fields['option_for'] = forms.ChoiceField(
                required=False,
                choices=[('', 'All Types')] + list(OptionForType.choices),
                widget=forms.Select(attrs={'class': 'form-control'})
            )
        elif model_type == 'filter_definition':
            self.fields['group'] = forms.ModelChoiceField(
                required=False,
                queryset=FilterGroup.objects.all().order_by('name'),
                empty_label='All Groups',
                widget=forms.Select(attrs={'class': 'form-control'})
            )
            self.fields['filter_type'] = forms.ChoiceField(
                required=False,
                choices=[('', 'All Types')] + list(FilterType.choices),
                widget=forms.Select(attrs={'class': 'form-control'})
            )
