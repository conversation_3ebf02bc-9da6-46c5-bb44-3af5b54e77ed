"""
URL patterns for filter management functionality
"""

from django.urls import path
from . import management_views

urlpatterns = [
    # Dashboard
    path('', management_views.filter_management_dashboard, name='filter_management_dashboard'),
    
    # Filter Groups Management
    path('filter-groups/', management_views.FilterGroupManagementView.as_view(), name='filter_group_management'),
    path('filter-groups/create/', management_views.FilterGroupCreateView.as_view(), name='filter_group_create'),
    path('filter-groups/<uuid:pk>/edit/', management_views.FilterGroupUpdateView.as_view(), name='filter_group_update'),
    path('filter-groups/<uuid:pk>/toggle-status/', management_views.toggle_filter_group_status, name='toggle_filter_group_status'),
    path('filter-groups/<uuid:pk>/delete/', management_views.delete_filter_group, name='delete_filter_group'),
    path('filter-groups/bulk-toggle/', management_views.bulk_toggle_filter_groups, name='bulk_toggle_filter_groups'),
    
    # Filter Definitions Management
    path('filter-definitions/', management_views.FilterDefinitionManagementView.as_view(), name='filter_definition_management'),
    path('filter-definitions/create/', management_views.FilterDefinitionCreateView.as_view(), name='filter_definition_create'),
    path('filter-definitions/<uuid:pk>/edit/', management_views.FilterDefinitionUpdateView.as_view(), name='filter_definition_update'),
    path('filter-definitions/<uuid:pk>/toggle-status/', management_views.toggle_filter_definition_status, name='toggle_filter_definition_status'),
    path('filter-definitions/<uuid:pk>/delete/', management_views.delete_filter_definition, name='delete_filter_definition'),
    path('filter-definitions/bulk-toggle/', management_views.bulk_toggle_filter_definitions, name='bulk_toggle_filter_definitions'),
]
