"""
Filter Management Views for CRUD operations and enable/disable functionality
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse, HttpResponseRedirect
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import transaction
from django.core.paginator import Paginator
import json

from .models import FilterGroup, FilterDefinition, LocationHierarchy
from .forms import FilterGroupForm, FilterDefinitionForm, LocationHierarchyForm


class FilterGroupManagementView(ListView):
    """List all filter groups with management options"""
    model = FilterGroup
    template_name = 'filter_catalog/management/filter_groups.html'
    context_object_name = 'filter_groups'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = FilterGroup.objects.all().order_by('channel', 'option_for', 'sort_order')
        
        # Apply filters
        channel = self.request.GET.get('channel')
        if channel:
            queryset = queryset.filter(channel=channel)
            
        option_for = self.request.GET.get('option_for')
        if option_for:
            queryset = queryset.filter(option_for=option_for)
            
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
            
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(name__icontains=search)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['channels'] = FilterGroup.objects.values_list('channel', flat=True).distinct()
        context['option_for_types'] = FilterGroup.objects.values_list('option_for', flat=True).distinct()
        context['current_filters'] = {
            'channel': self.request.GET.get('channel', ''),
            'option_for': self.request.GET.get('option_for', ''),
            'status': self.request.GET.get('status', ''),
            'search': self.request.GET.get('search', ''),
        }
        return context


class FilterGroupCreateView(CreateView):
    """Create new filter group"""
    model = FilterGroup
    form_class = FilterGroupForm
    template_name = 'filter_catalog/management/filter_group_form.html'
    success_url = reverse_lazy('filter_group_management')
    
    def form_valid(self, form):
        messages.success(self.request, f'Filter group "{form.instance.name}" created successfully!')
        return super().form_valid(form)


class FilterGroupUpdateView(UpdateView):
    """Update existing filter group"""
    model = FilterGroup
    form_class = FilterGroupForm
    template_name = 'filter_catalog/management/filter_group_form.html'
    success_url = reverse_lazy('filter_group_management')
    
    def form_valid(self, form):
        messages.success(self.request, f'Filter group "{form.instance.name}" updated successfully!')
        return super().form_valid(form)


class FilterDefinitionManagementView(ListView):
    """List all filter definitions with management options"""
    model = FilterDefinition
    template_name = 'filter_catalog/management/filter_definitions.html'
    context_object_name = 'filter_definitions'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = FilterDefinition.objects.select_related('group').all().order_by('group__name', 'sort_order')
        
        # Apply filters
        group_id = self.request.GET.get('group')
        if group_id:
            queryset = queryset.filter(group_id=group_id)
            
        filter_type = self.request.GET.get('filter_type')
        if filter_type:
            queryset = queryset.filter(filter_type=filter_type)
            
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
            
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(name__icontains=search)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_groups'] = FilterGroup.objects.all().order_by('name')
        context['filter_types'] = FilterDefinition.objects.values_list('filter_type', flat=True).distinct()
        context['current_filters'] = {
            'group': self.request.GET.get('group', ''),
            'filter_type': self.request.GET.get('filter_type', ''),
            'status': self.request.GET.get('status', ''),
            'search': self.request.GET.get('search', ''),
        }
        return context


class FilterDefinitionCreateView(CreateView):
    """Create new filter definition"""
    model = FilterDefinition
    form_class = FilterDefinitionForm
    template_name = 'filter_catalog/management/filter_definition_form.html'
    success_url = reverse_lazy('filter_definition_management')
    
    def form_valid(self, form):
        messages.success(self.request, f'Filter "{form.instance.name}" created successfully!')
        return super().form_valid(form)


class FilterDefinitionUpdateView(UpdateView):
    """Update existing filter definition"""
    model = FilterDefinition
    form_class = FilterDefinitionForm
    template_name = 'filter_catalog/management/filter_definition_form.html'
    success_url = reverse_lazy('filter_definition_management')
    
    def form_valid(self, form):
        messages.success(self.request, f'Filter "{form.instance.name}" updated successfully!')
        return super().form_valid(form)


@require_http_methods(["POST"])
def toggle_filter_group_status(request, pk):
    """Toggle active/inactive status of a filter group"""
    filter_group = get_object_or_404(FilterGroup, pk=pk)
    filter_group.is_active = not filter_group.is_active
    filter_group.save()
    
    status = "enabled" if filter_group.is_active else "disabled"
    messages.success(request, f'Filter group "{filter_group.name}" {status} successfully!')
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'is_active': filter_group.is_active,
            'message': f'Filter group {status} successfully!'
        })
    
    return redirect('filter_group_management')


@require_http_methods(["POST"])
def toggle_filter_definition_status(request, pk):
    """Toggle active/inactive status of a filter definition"""
    filter_def = get_object_or_404(FilterDefinition, pk=pk)
    filter_def.is_active = not filter_def.is_active
    filter_def.save()
    
    status = "enabled" if filter_def.is_active else "disabled"
    messages.success(request, f'Filter "{filter_def.name}" {status} successfully!')
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'is_active': filter_def.is_active,
            'message': f'Filter {status} successfully!'
        })
    
    return redirect('filter_definition_management')


@require_http_methods(["POST"])
def bulk_toggle_filter_groups(request):
    """Bulk enable/disable filter groups"""
    try:
        data = json.loads(request.body)
        group_ids = data.get('group_ids', [])
        action = data.get('action')  # 'enable' or 'disable'
        
        if not group_ids or action not in ['enable', 'disable']:
            return JsonResponse({'success': False, 'message': 'Invalid request'})
        
        is_active = action == 'enable'
        updated_count = FilterGroup.objects.filter(id__in=group_ids).update(is_active=is_active)
        
        action_text = "enabled" if is_active else "disabled"
        messages.success(request, f'{updated_count} filter groups {action_text} successfully!')
        
        return JsonResponse({
            'success': True,
            'message': f'{updated_count} filter groups {action_text} successfully!',
            'updated_count': updated_count
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@require_http_methods(["POST"])
def bulk_toggle_filter_definitions(request):
    """Bulk enable/disable filter definitions"""
    try:
        data = json.loads(request.body)
        filter_ids = data.get('filter_ids', [])
        action = data.get('action')  # 'enable' or 'disable'
        
        if not filter_ids or action not in ['enable', 'disable']:
            return JsonResponse({'success': False, 'message': 'Invalid request'})
        
        is_active = action == 'enable'
        updated_count = FilterDefinition.objects.filter(id__in=filter_ids).update(is_active=is_active)
        
        action_text = "enabled" if is_active else "disabled"
        messages.success(request, f'{updated_count} filters {action_text} successfully!')
        
        return JsonResponse({
            'success': True,
            'message': f'{updated_count} filters {action_text} successfully!',
            'updated_count': updated_count
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


def filter_management_dashboard(request):
    """Main dashboard for filter management"""
    context = {
        'total_groups': FilterGroup.objects.count(),
        'active_groups': FilterGroup.objects.filter(is_active=True).count(),
        'total_filters': FilterDefinition.objects.count(),
        'active_filters': FilterDefinition.objects.filter(is_active=True).count(),
        'total_locations': LocationHierarchy.objects.count(),
        'recent_groups': FilterGroup.objects.order_by('-updated_at')[:5],
        'recent_filters': FilterDefinition.objects.select_related('group').order_by('-updated_at')[:5],
    }
    return render(request, 'filter_catalog/management/dashboard.html', context)


@require_http_methods(["POST"])
def delete_filter_group(request, pk):
    """Delete a filter group"""
    filter_group = get_object_or_404(FilterGroup, pk=pk)
    group_name = filter_group.name
    
    try:
        with transaction.atomic():
            filter_group.delete()
        messages.success(request, f'Filter group "{group_name}" deleted successfully!')
    except Exception as e:
        messages.error(request, f'Error deleting filter group: {str(e)}')
    
    return redirect('filter_group_management')


@require_http_methods(["POST"])
def delete_filter_definition(request, pk):
    """Delete a filter definition"""
    filter_def = get_object_or_404(FilterDefinition, pk=pk)
    filter_name = filter_def.name
    
    try:
        with transaction.atomic():
            filter_def.delete()
        messages.success(request, f'Filter "{filter_name}" deleted successfully!')
    except Exception as e:
        messages.error(request, f'Error deleting filter: {str(e)}')
    
    return redirect('filter_definition_management')
