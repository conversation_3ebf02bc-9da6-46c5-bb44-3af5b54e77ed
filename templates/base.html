{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CreatorVerse Filter Utility{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .navbar-brand img {
            height: 24px;
            margin-right: 10px;
        }
        .filter-card, .fade-in {
            transition: transform 0.2s;
            margin-bottom: 20px;
        }
        /* Removed animation to prevent disappearing content */
        .filter-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .platform-badge {
            font-size: 0.8rem;
            margin-right: 5px;
        }
        .instagram { background: #E1306C; }
        .youtube { background: #FF0000; }
        .tiktok { background: #000000; }

        /* Enhanced Navigation */
        .modern-nav {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 0.5rem 0;
        }

        .brand-logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            text-decoration: none;
            color: white !important;
            transition: all 0.3s ease;
        }

        .brand-logo:hover {
            color: #f8f9fa !important;
            transform: scale(1.05);
        }

        .brand-icon {
            margin-right: 0.75rem;
            font-size: 1.75rem;
            color: #ffd700;
        }

        .brand-text {
            font-weight: 700;
        }

        .brand-subtitle {
            font-weight: 300;
            margin-left: 0.25rem;
            opacity: 0.9;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
            color: rgba(255,255,255,0.9) !important;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border-radius: 12px;
            border: none;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            padding: 0.5rem 0;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(4px);
        }
    </style>
    <link rel="stylesheet" href="{% static 'css/site_animations.css' %}">
    <link rel="stylesheet" href="{% static 'css/responsive-enhancements.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark modern-nav">
        <div class="container">
            <a class="navbar-brand brand-logo" href="{% url 'home' %}">
                <i class="fas fa-filter brand-icon"></i>
                <span class="brand-text">CreatorVerse</span>
                <span class="brand-subtitle">Filters</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" 
                           href="{% url 'home' %}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'analytics-dashboard' %}active{% endif %}" 
                           href="{% url 'analytics-dashboard' %}">Analytics</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'filter-group-list' %}active{% endif %}" 
                           href="{% url 'filter-group-list' %}">Filter Groups</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'location-list' %}active{% endif %}"
                           href="{% url 'location-list' %}">Locations</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'management' in request.resolver_match.url_name %}active{% endif %}"
                           href="{% url 'filter_management_dashboard' %}">Management</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="apiDropdown" role="button" data-bs-toggle="dropdown">
                            API
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'filter-config' %}?platform=instagram&option_for=creator" target="_blank">
                                Instagram Creator Filters
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'filter-config' %}?platform=youtube&option_for=creator" target="_blank">
                                YouTube Creator Filters
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'location-hierarchy' %}" target="_blank">
                                Location Hierarchy
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            Users
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'user_dashboard_home' %}">
                                Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'user_list' %}">
                                User Management
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organization_list' %}">
                                Organizations
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'brand_list' %}">
                                Brands
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>CreatorVerse Filter Utility</h5>
                    <p>A tool for managing filter configurations for creator discovery platform</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>&copy; {% now 'Y' %} CreatorVerse. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Site animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="{% static 'js/site_animations.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
