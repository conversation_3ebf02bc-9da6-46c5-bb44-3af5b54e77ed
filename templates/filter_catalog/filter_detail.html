{% extends 'base.html' %}

{% block title %}Filter Detail - {{ filter.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h1>{{ filter.name }}</h1>
            <p class="lead">{{ filter.full_path }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'filter-group-list' %}" class="btn btn-secondary">Back to Groups</a>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Filter Configuration</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Name:</th>
                            <td>{{ filter.name }}</td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td>
                                <span class="badge bg-primary">{{ filter.get_filter_type_display }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>Group:</th>
                            <td>{{ filter.group.name }}</td>
                        </tr>
                        <tr>
                            <th>Platform:</th>
                            <td>{{ filter.group.get_channel_display }}</td>
                        </tr>
                        <tr>
                            <th>Target:</th>
                            <td>{{ filter.group.get_option_for_display }}</td>
                        </tr>
                        <tr>
                            <th>Icon:</th>
                            <td>{{ filter.icon|default:"None" }}</td>
                        </tr>
                        <tr>
                            <th>Active:</th>
                            <td>
                                {% if filter.is_active %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-danger">No</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>UI Configuration</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Has Min/Max:</th>
                            <td>{% if filter.has_minmax %}Yes{% else %}No{% endif %}</td>
                        </tr>
                        <tr>
                            <th>Has Enter Value:</th>
                            <td>{% if filter.has_enter_value %}Yes{% else %}No{% endif %}</td>
                        </tr>
                        <tr>
                            <th>Has Search Box:</th>
                            <td>{% if filter.has_search_box %}Yes{% else %}No{% endif %}</td>
                        </tr>
                        <tr>
                            <th>Placeholder:</th>
                            <td>{{ filter.placeholder|default:"None" }}</td>
                        </tr>
                        <tr>
                            <th>Sort Order:</th>
                            <td>{{ filter.sort_order }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {% if filter.options %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Filter Options</h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded">{{ filter.options|pprint }}</pre>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Database Mapping</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Database Field:</th>
                            <td>{{ filter.db_field|default:"Not mapped" }}</td>
                        </tr>
                        <tr>
                            <th>API Field:</th>
                            <td>{{ filter.api_field|default:"Not mapped" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Metadata</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Created:</th>
                            <td>{{ filter.created_at }}</td>
                        </tr>
                        <tr>
                            <th>Updated:</th>
                            <td>{{ filter.updated_at }}</td>
                        </tr>
                        <tr>
                            <th>Filter ID:</th>
                            <td><code>{{ filter.id }}</code></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
