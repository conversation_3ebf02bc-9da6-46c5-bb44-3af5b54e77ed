{% extends 'base.html' %}

{% block title %}
    {% if object %}Edit Filter Definition{% else %}Add Filter Definition{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-filter"></i>
                        {% if object %}Edit Filter Definition{% else %}Add New Filter Definition{% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.group.id_for_label }}" class="form-label">
                                    {{ form.group.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.group }}
                                {% if form.group.help_text %}
                                    <div class="form-text">{{ form.group.help_text }}</div>
                                {% endif %}
                                {% if form.group.errors %}
                                    <div class="text-danger">
                                        {% for error in form.group.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.filter_type.id_for_label }}" class="form-label">
                                    {{ form.filter_type.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.filter_type }}
                                {% if form.filter_type.help_text %}
                                    <div class="form-text">{{ form.filter_type.help_text }}</div>
                                {% endif %}
                                {% if form.filter_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.filter_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.icon.id_for_label }}" class="form-label">
                                    {{ form.icon.label }}
                                </label>
                                {{ form.icon }}
                                {% if form.icon.help_text %}
                                    <div class="form-text">{{ form.icon.help_text }}</div>
                                {% endif %}
                                {% if form.icon.errors %}
                                    <div class="text-danger">
                                        {% for error in form.icon.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- UI Features -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">UI Features</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            {{ form.has_minmax }}
                                            <label for="{{ form.has_minmax.id_for_label }}" class="form-check-label">
                                                {{ form.has_minmax.label }}
                                            </label>
                                            {% if form.has_minmax.help_text %}
                                                <div class="form-text">{{ form.has_minmax.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            {{ form.has_enter_value }}
                                            <label for="{{ form.has_enter_value.id_for_label }}" class="form-check-label">
                                                {{ form.has_enter_value.label }}
                                            </label>
                                            {% if form.has_enter_value.help_text %}
                                                <div class="form-text">{{ form.has_enter_value.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            {{ form.has_search_box }}
                                            <label for="{{ form.has_search_box.id_for_label }}" class="form-check-label">
                                                {{ form.has_search_box.label }}
                                            </label>
                                            {% if form.has_search_box.help_text %}
                                                <div class="form-text">{{ form.has_search_box.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-12 mb-3">
                                        <label for="{{ form.placeholder.id_for_label }}" class="form-label">
                                            {{ form.placeholder.label }}
                                        </label>
                                        {{ form.placeholder }}
                                        {% if form.placeholder.help_text %}
                                            <div class="form-text">{{ form.placeholder.help_text }}</div>
                                        {% endif %}
                                        {% if form.placeholder.errors %}
                                            <div class="text-danger">
                                                {% for error in form.placeholder.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Options -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Filter Options</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="{{ form.options_json.id_for_label }}" class="form-label">
                                        {{ form.options_json.label }}
                                    </label>
                                    {{ form.options_json }}
                                    {% if form.options_json.help_text %}
                                        <div class="form-text">{{ form.options_json.help_text }}</div>
                                    {% endif %}
                                    {% if form.options_json.errors %}
                                        <div class="text-danger">
                                            {% for error in form.options_json.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="alert alert-info">
                                    <strong>Example format:</strong>
                                    <pre class="mb-0">[
  {"label": "Male", "value": "male", "description": "Male creators"},
  {"label": "Female", "value": "female", "description": "Female creators"}
]</pre>
                                </div>
                            </div>
                        </div>

                        <!-- Database Mapping -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Database Mapping</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.db_field.id_for_label }}" class="form-label">
                                            {{ form.db_field.label }}
                                        </label>
                                        {{ form.db_field }}
                                        {% if form.db_field.help_text %}
                                            <div class="form-text">{{ form.db_field.help_text }}</div>
                                        {% endif %}
                                        {% if form.db_field.errors %}
                                            <div class="text-danger">
                                                {% for error in form.db_field.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.api_field.id_for_label }}" class="form-label">
                                            {{ form.api_field.label }}
                                        </label>
                                        {{ form.api_field }}
                                        {% if form.api_field.help_text %}
                                            <div class="form-text">{{ form.api_field.help_text }}</div>
                                        {% endif %}
                                        {% if form.api_field.errors %}
                                            <div class="text-danger">
                                                {% for error in form.api_field.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.sort_order.id_for_label }}" class="form-label">
                                    {{ form.sort_order.label }}
                                </label>
                                {{ form.sort_order }}
                                {% if form.sort_order.help_text %}
                                    <div class="form-text">{{ form.sort_order.help_text }}</div>
                                {% endif %}
                                {% if form.sort_order.errors %}
                                    <div class="text-danger">
                                        {% for error in form.sort_order.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    {{ form.is_active }}
                                    <label for="{{ form.is_active.id_for_label }}" class="form-check-label">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.help_text %}
                                        <div class="form-text">{{ form.is_active.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_active.errors %}
                                        <div class="text-danger">
                                            {% for error in form.is_active.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'filter_definition_management' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i>
                                {% if object %}Update Filter{% else %}Create Filter{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
}

.text-danger {
    font-size: 0.875rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 0.375rem;
}

pre {
    font-size: 0.875rem;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format JSON in options field
    const optionsField = document.getElementById('{{ form.options_json.id_for_label }}');
    if (optionsField) {
        optionsField.addEventListener('blur', function() {
            try {
                const value = this.value.trim();
                if (value) {
                    const parsed = JSON.parse(value);
                    this.value = JSON.stringify(parsed, null, 2);
                }
            } catch (e) {
                // Invalid JSON, leave as is
            }
        });
    }

    // Show/hide relevant fields based on filter type
    const filterTypeField = document.getElementById('{{ form.filter_type.id_for_label }}');
    const searchBoxField = document.getElementById('{{ form.has_search_box.id_for_label }}');
    
    function updateFieldVisibility() {
        const filterType = filterTypeField.value;
        
        // Multilevel checkbox requires search box
        if (filterType === 'multilevel-checkbox') {
            searchBoxField.checked = true;
            searchBoxField.disabled = true;
        } else {
            searchBoxField.disabled = false;
        }
    }
    
    if (filterTypeField) {
        filterTypeField.addEventListener('change', updateFieldVisibility);
        updateFieldVisibility(); // Initial call
    }
});
</script>
{% endblock %}
