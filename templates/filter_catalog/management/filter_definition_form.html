{% extends 'base.html' %}

{% block title %}
    {% if object %}Edit Filter Definition{% else %}Add Filter Definition{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-filter"></i>
                        {% if object %}Edit Filter Definition{% else %}Add New Filter Definition{% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle"></i> Basic Information
                                </h5>
                                <p class="section-description">Define the core properties of your filter</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-group-enhanced">
                                        <label for="{{ form.group.id_for_label }}" class="form-label">
                                            <i class="fas fa-layer-group text-primary"></i>
                                            {{ form.group.label }} <span class="text-danger">*</span>
                                        </label>
                                        {{ form.group }}
                                        {% if form.group.help_text %}
                                            <div class="form-text">{{ form.group.help_text }}</div>
                                        {% endif %}
                                        {% if form.group.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.group.errors %}
                                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.filter_type.id_for_label }}" class="form-label">
                                    {{ form.filter_type.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.filter_type }}
                                {% if form.filter_type.help_text %}
                                    <div class="form-text">{{ form.filter_type.help_text }}</div>
                                {% endif %}
                                {% if form.filter_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.filter_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.icon.id_for_label }}" class="form-label">
                                    {{ form.icon.label }}
                                </label>
                                {{ form.icon }}
                                {% if form.icon.help_text %}
                                    <div class="form-text">{{ form.icon.help_text }}</div>
                                {% endif %}
                                {% if form.icon.errors %}
                                    <div class="text-danger">
                                        {% for error in form.icon.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- UI Features -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">UI Features</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            {{ form.has_minmax }}
                                            <label for="{{ form.has_minmax.id_for_label }}" class="form-check-label">
                                                {{ form.has_minmax.label }}
                                            </label>
                                            {% if form.has_minmax.help_text %}
                                                <div class="form-text">{{ form.has_minmax.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            {{ form.has_enter_value }}
                                            <label for="{{ form.has_enter_value.id_for_label }}" class="form-check-label">
                                                {{ form.has_enter_value.label }}
                                            </label>
                                            {% if form.has_enter_value.help_text %}
                                                <div class="form-text">{{ form.has_enter_value.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            {{ form.has_search_box }}
                                            <label for="{{ form.has_search_box.id_for_label }}" class="form-check-label">
                                                {{ form.has_search_box.label }}
                                            </label>
                                            {% if form.has_search_box.help_text %}
                                                <div class="form-text">{{ form.has_search_box.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-12 mb-3">
                                        <label for="{{ form.placeholder.id_for_label }}" class="form-label">
                                            {{ form.placeholder.label }}
                                        </label>
                                        {{ form.placeholder }}
                                        {% if form.placeholder.help_text %}
                                            <div class="form-text">{{ form.placeholder.help_text }}</div>
                                        {% endif %}
                                        {% if form.placeholder.errors %}
                                            <div class="text-danger">
                                                {% for error in form.placeholder.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Options -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Filter Options</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="{{ form.options_json.id_for_label }}" class="form-label">
                                        {{ form.options_json.label }}
                                    </label>
                                    {{ form.options_json }}
                                    {% if form.options_json.help_text %}
                                        <div class="form-text">{{ form.options_json.help_text }}</div>
                                    {% endif %}
                                    {% if form.options_json.errors %}
                                        <div class="text-danger">
                                            {% for error in form.options_json.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="alert alert-info">
                                    <strong>Example format:</strong>
                                    <pre class="mb-0">[
  {"label": "Male", "value": "male", "description": "Male creators"},
  {"label": "Female", "value": "female", "description": "Female creators"}
]</pre>
                                </div>
                            </div>
                        </div>

                        <!-- Database Mapping -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Database Mapping</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.db_field.id_for_label }}" class="form-label">
                                            {{ form.db_field.label }}
                                        </label>
                                        {{ form.db_field }}
                                        {% if form.db_field.help_text %}
                                            <div class="form-text">{{ form.db_field.help_text }}</div>
                                        {% endif %}
                                        {% if form.db_field.errors %}
                                            <div class="text-danger">
                                                {% for error in form.db_field.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.api_field.id_for_label }}" class="form-label">
                                            {{ form.api_field.label }}
                                        </label>
                                        {{ form.api_field }}
                                        {% if form.api_field.help_text %}
                                            <div class="form-text">{{ form.api_field.help_text }}</div>
                                        {% endif %}
                                        {% if form.api_field.errors %}
                                            <div class="text-danger">
                                                {% for error in form.api_field.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.sort_order.id_for_label }}" class="form-label">
                                    {{ form.sort_order.label }}
                                </label>
                                {{ form.sort_order }}
                                {% if form.sort_order.help_text %}
                                    <div class="form-text">{{ form.sort_order.help_text }}</div>
                                {% endif %}
                                {% if form.sort_order.errors %}
                                    <div class="text-danger">
                                        {% for error in form.sort_order.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    {{ form.is_active }}
                                    <label for="{{ form.is_active.id_for_label }}" class="form-check-label">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.help_text %}
                                        <div class="form-text">{{ form.is_active.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_active.errors %}
                                        <div class="text-danger">
                                            {% for error in form.is_active.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'filter_definition_management' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i>
                                {% if object %}Update Filter{% else %}Create Filter{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced Form Styling */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.section-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.section-title {
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.section-title i {
    color: #667eea;
}

.section-description {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.form-group-enhanced {
    position: relative;
}

.form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label i {
    font-size: 0.875rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.form-text::before {
    content: "💡";
    font-size: 0.75rem;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.invalid-feedback i {
    margin-right: 0.25rem;
}

/* Enhanced Cards */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h6 {
    margin-bottom: 0;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Enhanced Checkboxes */
.form-check {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.form-check:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-label {
    font-weight: 500;
    color: #2d3748;
    cursor: pointer;
}

/* Enhanced Alerts */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    border-left: 4px solid #2196f3;
}

.alert-danger {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #c62828;
    border-left: 4px solid #f44336;
}

/* Code Blocks */
pre {
    font-size: 0.875rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .section-header {
        text-align: left;
    }

    .section-title {
        justify-content: flex-start;
        font-size: 1.1rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Animation for form sections */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-section {
    animation: slideInUp 0.6s ease forwards;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }
.form-section:nth-child(4) { animation-delay: 0.4s; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format JSON in options field
    const optionsField = document.getElementById('{{ form.options_json.id_for_label }}');
    if (optionsField) {
        optionsField.addEventListener('blur', function() {
            try {
                const value = this.value.trim();
                if (value) {
                    const parsed = JSON.parse(value);
                    this.value = JSON.stringify(parsed, null, 2);
                }
            } catch (e) {
                // Invalid JSON, leave as is
            }
        });
    }

    // Show/hide relevant fields based on filter type
    const filterTypeField = document.getElementById('{{ form.filter_type.id_for_label }}');
    const searchBoxField = document.getElementById('{{ form.has_search_box.id_for_label }}');
    
    function updateFieldVisibility() {
        const filterType = filterTypeField.value;
        
        // Multilevel checkbox requires search box
        if (filterType === 'multilevel-checkbox') {
            searchBoxField.checked = true;
            searchBoxField.disabled = true;
        } else {
            searchBoxField.disabled = false;
        }
    }
    
    if (filterTypeField) {
        filterTypeField.addEventListener('change', updateFieldVisibility);
        updateFieldVisibility(); // Initial call
    }
});
</script>
{% endblock %}
