{% extends 'base.html' %}

{% block title %}Manage Filter Groups{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-layer-group"></i> Manage Filter Groups</h1>
                <div>
                    <a href="{% url 'filter_management_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'filter_group_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Group
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ current_filters.search }}" placeholder="Search by name...">
                        </div>
                        <div class="col-md-2">
                            <label for="channel" class="form-label">Platform</label>
                            <select class="form-control" id="channel" name="channel">
                                <option value="">All Platforms</option>
                                {% for channel in channels %}
                                    <option value="{{ channel }}" {% if current_filters.channel == channel %}selected{% endif %}>
                                        {{ channel|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="option_for" class="form-label">Type</label>
                            <select class="form-control" id="option_for" name="option_for">
                                <option value="">All Types</option>
                                {% for option_for in option_for_types %}
                                    <option value="{{ option_for }}" {% if current_filters.option_for == option_for %}selected{% endif %}>
                                        {{ option_for|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All</option>
                                <option value="active" {% if current_filters.status == 'active' %}selected{% endif %}>Active</option>
                                <option value="inactive" {% if current_filters.status == 'inactive' %}selected{% endif %}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{% url 'filter_group_management' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button type="button" class="btn btn-success btn-sm" id="bulk-enable" disabled>
                        <i class="fas fa-check"></i> Enable Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-disable" disabled>
                        <i class="fas fa-times"></i> Disable Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <div>
                    <span class="text-muted">{{ filter_groups|length }} group(s) found</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Groups Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if filter_groups %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <th>Name</th>
                                        <th>Platform</th>
                                        <th>Type</th>
                                        <th>Sort Order</th>
                                        <th>Status</th>
                                        <th>Filters</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for group in filter_groups %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input group-checkbox" 
                                                       value="{{ group.id }}">
                                            </td>
                                            <td>
                                                <strong>{{ group.name }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ group.channel|title }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ group.option_for|title }}</span>
                                            </td>
                                            <td>{{ group.sort_order }}</td>
                                            <td>
                                                {% if group.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ group.filters.count }}</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ group.updated_at|date:"M d, Y" }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{% url 'filter_group_update' group.pk %}"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-warning toggle-status"
                                                            data-id="{{ group.id }}" data-active="{{ group.is_active }}"
                                                            title="{% if group.is_active %}Disable{% else %}Enable{% endif %}">
                                                        {% if group.is_active %}
                                                            <i class="fas fa-pause"></i>
                                                        {% else %}
                                                            <i class="fas fa-play"></i>
                                                        {% endif %}
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger delete-group"
                                                            data-id="{{ group.id }}" data-name="{{ group.name }}"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">First</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Previous</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Next</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Last</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No filter groups found</h5>
                            <p class="text-muted">Create your first filter group to get started.</p>
                            <a href="{% url 'filter_group_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Filter Group
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the filter group "<span id="delete-group-name"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone and will also delete all filters in this group.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" id="delete-form" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    const groupCheckboxes = document.querySelectorAll('.group-checkbox');
    const bulkButtons = document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete');

    selectAllCheckbox.addEventListener('change', function() {
        groupCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButtons();
    });

    groupCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });

    function updateBulkButtons() {
        const checkedBoxes = document.querySelectorAll('.group-checkbox:checked');
        const hasSelection = checkedBoxes.length > 0;
        
        bulkButtons.forEach(button => {
            button.disabled = !hasSelection;
        });
    }

    // Handle toggle status
    document.querySelectorAll('.toggle-status').forEach(button => {
        button.addEventListener('click', function() {
            const groupId = this.dataset.id;
            const isActive = this.dataset.active === 'true';
            
            fetch(`/management/filter-groups/${groupId}/toggle-status/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });
    });

    // Handle delete
    document.querySelectorAll('.delete-group').forEach(button => {
        button.addEventListener('click', function() {
            const groupId = this.dataset.id;
            const groupName = this.dataset.name;
            
            document.getElementById('delete-group-name').textContent = groupName;
            document.getElementById('delete-form').action = `/management/filter-groups/${groupId}/delete/`;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });

    // Handle bulk actions
    document.getElementById('bulk-enable').addEventListener('click', function() {
        performBulkAction('enable');
    });

    document.getElementById('bulk-disable').addEventListener('click', function() {
        performBulkAction('disable');
    });

    document.getElementById('bulk-delete').addEventListener('click', function() {
        if (confirm('Are you sure you want to delete the selected filter groups? This action cannot be undone.')) {
            performBulkAction('delete');
        }
    });

    function performBulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.group-checkbox:checked');
        const groupIds = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (groupIds.length === 0) {
            alert('Please select at least one filter group.');
            return;
        }

        fetch('/management/filter-groups/bulk-toggle/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                group_ids: groupIds,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
});
</script>
{% endblock %}
