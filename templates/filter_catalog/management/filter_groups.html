{% extends 'base.html' %}

{% block title %}Manage Filter Groups{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb modern-breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fas fa-home"></i> Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'filter_management_dashboard' %}"><i class="fas fa-cogs"></i> Management</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-layer-group"></i> Filter Groups
            </li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <div class="page-header-with-actions">
                <div class="page-info">
                    <h1 class="page-title"><i class="fas fa-layer-group"></i> Manage Filter Groups</h1>
                    <p class="page-subtitle">Organize filters into logical groups for better management</p>
                </div>
                <div class="page-actions">
                    <a href="{% url 'filter_management_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'filter_group_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Group
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ current_filters.search }}" placeholder="Search by name...">
                        </div>
                        <div class="col-md-2">
                            <label for="channel" class="form-label">Platform</label>
                            <select class="form-control" id="channel" name="channel">
                                <option value="">All Platforms</option>
                                {% for channel in channels %}
                                    <option value="{{ channel }}" {% if current_filters.channel == channel %}selected{% endif %}>
                                        {{ channel|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="option_for" class="form-label">Type</label>
                            <select class="form-control" id="option_for" name="option_for">
                                <option value="">All Types</option>
                                {% for option_for in option_for_types %}
                                    <option value="{{ option_for }}" {% if current_filters.option_for == option_for %}selected{% endif %}>
                                        {{ option_for|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All</option>
                                <option value="active" {% if current_filters.status == 'active' %}selected{% endif %}>Active</option>
                                <option value="inactive" {% if current_filters.status == 'inactive' %}selected{% endif %}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{% url 'filter_group_management' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button type="button" class="btn btn-success btn-sm" id="bulk-enable" disabled>
                        <i class="fas fa-check"></i> Enable Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-disable" disabled>
                        <i class="fas fa-times"></i> Disable Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <div>
                    <span class="text-muted">{{ filter_groups|length }} group(s) found</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Groups Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if filter_groups %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <th>Name</th>
                                        <th>Platform</th>
                                        <th>Type</th>
                                        <th>Sort Order</th>
                                        <th>Status</th>
                                        <th>Filters</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for group in filter_groups %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input group-checkbox" 
                                                       value="{{ group.id }}">
                                            </td>
                                            <td>
                                                <strong>{{ group.name }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ group.channel|title }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ group.option_for|title }}</span>
                                            </td>
                                            <td>{{ group.sort_order }}</td>
                                            <td>
                                                {% if group.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ group.filters.count }}</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ group.updated_at|date:"M d, Y" }}</small>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="{% url 'filter_group_update' group.pk %}"
                                                       class="btn btn-sm btn-primary action-btn"
                                                       title="Edit Group" data-bs-toggle="tooltip">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm {% if group.is_active %}btn-warning{% else %}btn-success{% endif %} action-btn toggle-status"
                                                            data-id="{{ group.id }}" data-active="{{ group.is_active }}"
                                                            title="{% if group.is_active %}Disable Group{% else %}Enable Group{% endif %}"
                                                            data-bs-toggle="tooltip">
                                                        {% if group.is_active %}
                                                            <i class="fas fa-pause"></i>
                                                        {% else %}
                                                            <i class="fas fa-play"></i>
                                                        {% endif %}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger action-btn delete-group"
                                                            data-id="{{ group.id }}" data-name="{{ group.name }}"
                                                            title="Delete Group" data-bs-toggle="tooltip">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">First</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Previous</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Next</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.channel %}&channel={{ request.GET.channel }}{% endif %}{% if request.GET.option_for %}&option_for={{ request.GET.option_for }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Last</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No filter groups found</h5>
                            <p class="text-muted">Create your first filter group to get started.</p>
                            <a href="{% url 'filter_group_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Filter Group
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the filter group "<span id="delete-group-name"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone and will also delete all filters in this group.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" id="delete-form" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<style>
/* Enhanced Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-btn i {
    font-size: 0.875rem;
}

/* Enhanced Table Styling */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.001);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Enhanced Cards */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Enhanced Form Controls */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
    }

    .table-responsive {
        border-radius: 8px;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Enhanced Breadcrumbs */
.modern-breadcrumb {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.modern-breadcrumb .breadcrumb-item {
    font-weight: 500;
}

.modern-breadcrumb .breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.2s ease;
}

.modern-breadcrumb .breadcrumb-item a:hover {
    color: #764ba2;
    transform: translateX(2px);
}

.modern-breadcrumb .breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

.modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: 600;
}

/* Page Header with Actions */
.page-header-with-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 0;
}

.page-info .page-title {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.page-info .page-title i {
    color: #667eea;
    margin-right: 0.75rem;
}

.page-info .page-subtitle {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 0;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Enhanced Pagination */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #667eea;
    transition: all 0.2s ease;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
}

/* Responsive Design for Page Header */
@media (max-width: 768px) {
    .page-header-with-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .page-info .page-title {
        font-size: 1.5rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    const groupCheckboxes = document.querySelectorAll('.group-checkbox');
    const bulkButtons = document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete');

    selectAllCheckbox.addEventListener('change', function() {
        groupCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButtons();
    });

    groupCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });

    function updateBulkButtons() {
        const checkedBoxes = document.querySelectorAll('.group-checkbox:checked');
        const hasSelection = checkedBoxes.length > 0;
        
        bulkButtons.forEach(button => {
            button.disabled = !hasSelection;
        });
    }

    // Handle toggle status
    document.querySelectorAll('.toggle-status').forEach(button => {
        button.addEventListener('click', function() {
            const groupId = this.dataset.id;
            const isActive = this.dataset.active === 'true';
            
            fetch(`/management/filter-groups/${groupId}/toggle-status/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });
    });

    // Handle delete
    document.querySelectorAll('.delete-group').forEach(button => {
        button.addEventListener('click', function() {
            const groupId = this.dataset.id;
            const groupName = this.dataset.name;
            
            document.getElementById('delete-group-name').textContent = groupName;
            document.getElementById('delete-form').action = `/management/filter-groups/${groupId}/delete/`;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });

    // Handle bulk actions
    document.getElementById('bulk-enable').addEventListener('click', function() {
        performBulkAction('enable');
    });

    document.getElementById('bulk-disable').addEventListener('click', function() {
        performBulkAction('disable');
    });

    document.getElementById('bulk-delete').addEventListener('click', function() {
        if (confirm('Are you sure you want to delete the selected filter groups? This action cannot be undone.')) {
            performBulkAction('delete');
        }
    });

    function performBulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.group-checkbox:checked');
        const groupIds = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (groupIds.length === 0) {
            alert('Please select at least one filter group.');
            return;
        }

        fetch('/management/filter-groups/bulk-toggle/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                group_ids: groupIds,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
});
</script>
{% endblock %}
