{% extends 'base.html' %}

{% block title %}Filter Management Dashboard{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb modern-breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fas fa-home"></i> Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-cogs"></i> Management Dashboard
            </li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-filter"></i> Filter Management Dashboard
                </h1>
                <p class="page-subtitle">Manage your filter groups, definitions, and system configuration</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 fw-bold">{{ total_groups }}</h3>
                            <p class="mb-0 opacity-75">Total Groups</p>
                            <small class="opacity-75">{{ active_groups }} active</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-layer-group fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white opacity-50" style="width: {% widthratio active_groups total_groups 100 %}%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 fw-bold">{{ total_filters }}</h3>
                            <p class="mb-0 opacity-75">Total Filters</p>
                            <small class="opacity-75">{{ active_filters }} active</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-filter fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white opacity-50" style="width: {% widthratio active_filters total_filters 100 %}%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 fw-bold">{{ total_locations }}</h3>
                            <p class="mb-0 opacity-75">Locations</p>
                            <small class="opacity-75">Hierarchy nodes</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-map-marker-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white opacity-50" style="width: 85%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 fw-bold">{% widthratio active_filters total_filters 100 %}%</h3>
                            <p class="mb-0 opacity-75">Active Rate</p>
                            <small class="opacity-75">Filter activation</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white opacity-50" style="width: {% widthratio active_filters total_filters 100 %}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Filter Groups</h6>
                            <div class="btn-group mb-3" role="group">
                                <a href="{% url 'filter_group_management' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-list"></i> Manage Groups
                                </a>
                                <a href="{% url 'filter_group_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add Group
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Filter Definitions</h6>
                            <div class="btn-group mb-3" role="group">
                                <a href="{% url 'filter_definition_management' %}" class="btn btn-outline-success">
                                    <i class="fas fa-list"></i> Manage Filters
                                </a>
                                <a href="{% url 'filter_definition_create' %}" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Filter
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Filter Groups</h5>
                </div>
                <div class="card-body">
                    {% if recent_groups %}
                        <div class="list-group list-group-flush">
                            {% for group in recent_groups %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ group.name }}</h6>
                                        <small class="text-muted">{{ group.channel|title }} - {{ group.option_for|title }}</small>
                                    </div>
                                    <div>
                                        {% if group.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No filter groups found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Filters</h5>
                </div>
                <div class="card-body">
                    {% if recent_filters %}
                        <div class="list-group list-group-flush">
                            {% for filter in recent_filters %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ filter.name }}</h6>
                                        <small class="text-muted">{{ filter.group.name }} - {{ filter.filter_type|title }}</small>
                                    </div>
                                    <div>
                                        {% if filter.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No filters found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced Dashboard Styling */
.stat-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.stat-icon {
    opacity: 0.7;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    opacity: 1;
    transform: scale(1.1);
}

.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h5, .card-header h6 {
    margin-bottom: 0;
    font-weight: 600;
    color: #495057;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-success {
    border: 2px solid #11998e;
    color: #11998e;
    background: transparent;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border-color: transparent;
    color: white;
}

.btn-group .btn {
    margin-right: 0.5rem;
}

/* Enhanced List Groups */
.list-group-item {
    border-left: none;
    border-right: none;
    border-radius: 0;
    transition: all 0.2s ease;
    padding: 1rem 1.5rem;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.list-group-item:first-child {
    border-top: none;
    border-radius: 0;
}

.list-group-item:last-child {
    border-bottom: none;
    border-radius: 0;
}

.list-group-item h6 {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.list-group-item small {
    color: #6c757d;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.badge.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* Enhanced Breadcrumbs */
.modern-breadcrumb {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.modern-breadcrumb .breadcrumb-item {
    font-weight: 500;
}

.modern-breadcrumb .breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.2s ease;
}

.modern-breadcrumb .breadcrumb-item a:hover {
    color: #764ba2;
    transform: translateX(2px);
}

.modern-breadcrumb .breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

.modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: 600;
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
}

.page-title i {
    color: #667eea;
    margin-right: 0.75rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card, .card {
    animation: fadeInUp 0.6s ease forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
</style>
{% endblock %}
