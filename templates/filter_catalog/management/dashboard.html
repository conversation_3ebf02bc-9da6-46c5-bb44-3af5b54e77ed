{% extends 'base.html' %}

{% block title %}Filter Management Dashboard{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-filter"></i> Filter Management Dashboard
            </h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_groups }}</h4>
                            <p class="mb-0">Total Groups</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                    <small>{{ active_groups }} active</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_filters }}</h4>
                            <p class="mb-0">Total Filters</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-filter fa-2x"></i>
                        </div>
                    </div>
                    <small>{{ active_filters }} active</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_locations }}</h4>
                            <p class="mb-0">Locations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                    <small>Hierarchy nodes</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ active_filters|floatformat:0 }}%</h4>
                            <p class="mb-0">Active Rate</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                    <small>Filter activation</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Filter Groups</h6>
                            <div class="btn-group mb-3" role="group">
                                <a href="{% url 'filter_group_management' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-list"></i> Manage Groups
                                </a>
                                <a href="{% url 'filter_group_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add Group
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Filter Definitions</h6>
                            <div class="btn-group mb-3" role="group">
                                <a href="{% url 'filter_definition_management' %}" class="btn btn-outline-success">
                                    <i class="fas fa-list"></i> Manage Filters
                                </a>
                                <a href="{% url 'filter_definition_create' %}" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Filter
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Filter Groups</h5>
                </div>
                <div class="card-body">
                    {% if recent_groups %}
                        <div class="list-group list-group-flush">
                            {% for group in recent_groups %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ group.name }}</h6>
                                        <small class="text-muted">{{ group.channel|title }} - {{ group.option_for|title }}</small>
                                    </div>
                                    <div>
                                        {% if group.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No filter groups found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Filters</h5>
                </div>
                <div class="card-body">
                    {% if recent_filters %}
                        <div class="list-group list-group-flush">
                            {% for filter in recent_filters %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ filter.name }}</h6>
                                        <small class="text-muted">{{ filter.group.name }} - {{ filter.filter_type|title }}</small>
                                    </div>
                                    <div>
                                        {% if filter.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No filters found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body h4 {
    font-size: 2rem;
    font-weight: bold;
}

.btn-group .btn {
    margin-right: 0.5rem;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
