{% extends 'base.html' %}

{% block title %}Manage Filter Definitions{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-filter"></i> Manage Filter Definitions</h1>
                <div>
                    <a href="{% url 'filter_management_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'filter_definition_create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New Filter
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ current_filters.search }}" placeholder="Search by name...">
                        </div>
                        <div class="col-md-3">
                            <label for="group" class="form-label">Filter Group</label>
                            <select class="form-control" id="group" name="group">
                                <option value="">All Groups</option>
                                {% for group in filter_groups %}
                                    <option value="{{ group.id }}" {% if current_filters.group == group.id|stringformat:"s" %}selected{% endif %}>
                                        {{ group.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_type" class="form-label">Type</label>
                            <select class="form-control" id="filter_type" name="filter_type">
                                <option value="">All Types</option>
                                {% for type in filter_types %}
                                    <option value="{{ type }}" {% if current_filters.filter_type == type %}selected{% endif %}>
                                        {{ type|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All</option>
                                <option value="active" {% if current_filters.status == 'active' %}selected{% endif %}>Active</option>
                                <option value="inactive" {% if current_filters.status == 'inactive' %}selected{% endif %}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{% url 'filter_definition_management' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button type="button" class="btn btn-success btn-sm" id="bulk-enable" disabled>
                        <i class="fas fa-check"></i> Enable Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-disable" disabled>
                        <i class="fas fa-times"></i> Disable Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <div>
                    <span class="text-muted">{{ filter_definitions|length }} filter(s) found</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Definitions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if filter_definitions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <th>Name</th>
                                        <th>Group</th>
                                        <th>Type</th>
                                        <th>Features</th>
                                        <th>Sort Order</th>
                                        <th>Status</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for filter in filter_definitions %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input filter-checkbox" 
                                                       value="{{ filter.id }}">
                                            </td>
                                            <td>
                                                <strong>{{ filter.name }}</strong>
                                                {% if filter.icon %}
                                                    <i class="{{ filter.icon }} text-muted ms-1"></i>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ filter.group.name }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ filter.get_filter_type_display }}</span>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    {% if filter.has_minmax %}
                                                        <span class="badge bg-secondary" title="Has Min/Max">MM</span>
                                                    {% endif %}
                                                    {% if filter.has_enter_value %}
                                                        <span class="badge bg-secondary" title="Has Enter Value">EV</span>
                                                    {% endif %}
                                                    {% if filter.has_search_box %}
                                                        <span class="badge bg-secondary" title="Has Search Box">SB</span>
                                                    {% endif %}
                                                    {% if filter.options %}
                                                        <span class="badge bg-secondary" title="Has Options">{{ filter.options|length }} opts</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>{{ filter.sort_order }}</td>
                                            <td>
                                                {% if filter.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ filter.updated_at|date:"M d, Y" }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{% url 'filter_definition_update' filter.pk %}"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-warning toggle-status"
                                                            data-id="{{ filter.id }}" data-active="{{ filter.is_active }}"
                                                            title="{% if filter.is_active %}Disable{% else %}Enable{% endif %}">
                                                        {% if filter.is_active %}
                                                            <i class="fas fa-pause"></i>
                                                        {% else %}
                                                            <i class="fas fa-play"></i>
                                                        {% endif %}
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger delete-filter"
                                                            data-id="{{ filter.id }}" data-name="{{ filter.name }}"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">First</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Previous</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Next</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Last</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-filter fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No filter definitions found</h5>
                            <p class="text-muted">Create your first filter definition to get started.</p>
                            <a href="{% url 'filter_definition_create' %}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Filter Definition
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the filter "<span id="delete-filter-name"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" id="delete-form" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
    const bulkButtons = document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete');

    selectAllCheckbox.addEventListener('change', function() {
        filterCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButtons();
    });

    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });

    function updateBulkButtons() {
        const checkedBoxes = document.querySelectorAll('.filter-checkbox:checked');
        const hasSelection = checkedBoxes.length > 0;
        
        bulkButtons.forEach(button => {
            button.disabled = !hasSelection;
        });
    }

    // Handle toggle status
    document.querySelectorAll('.toggle-status').forEach(button => {
        button.addEventListener('click', function() {
            const filterId = this.dataset.id;
            const isActive = this.dataset.active === 'true';
            
            fetch(`/management/filter-definitions/${filterId}/toggle-status/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });
    });

    // Handle delete
    document.querySelectorAll('.delete-filter').forEach(button => {
        button.addEventListener('click', function() {
            const filterId = this.dataset.id;
            const filterName = this.dataset.name;
            
            document.getElementById('delete-filter-name').textContent = filterName;
            document.getElementById('delete-form').action = `/management/filter-definitions/${filterId}/delete/`;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });

    // Handle bulk actions
    document.getElementById('bulk-enable').addEventListener('click', function() {
        performBulkAction('enable');
    });

    document.getElementById('bulk-disable').addEventListener('click', function() {
        performBulkAction('disable');
    });

    document.getElementById('bulk-delete').addEventListener('click', function() {
        if (confirm('Are you sure you want to delete the selected filters? This action cannot be undone.')) {
            performBulkAction('delete');
        }
    });

    function performBulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.filter-checkbox:checked');
        const filterIds = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (filterIds.length === 0) {
            alert('Please select at least one filter.');
            return;
        }

        fetch('/management/filter-definitions/bulk-toggle/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                filter_ids: filterIds,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
});
</script>
{% endblock %}
