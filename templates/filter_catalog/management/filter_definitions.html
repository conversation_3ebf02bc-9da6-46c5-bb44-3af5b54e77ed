{% extends 'base.html' %}

{% block title %}Manage Filter Definitions{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-filter"></i> Manage Filter Definitions</h1>
                <div>
                    <a href="{% url 'filter_management_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'filter_definition_create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New Filter
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ current_filters.search }}" placeholder="Search by name...">
                        </div>
                        <div class="col-md-3">
                            <label for="group" class="form-label">Filter Group</label>
                            <select class="form-control" id="group" name="group">
                                <option value="">All Groups</option>
                                {% for group in filter_groups %}
                                    <option value="{{ group.id }}" {% if current_filters.group == group.id|stringformat:"s" %}selected{% endif %}>
                                        {{ group.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_type" class="form-label">Type</label>
                            <select class="form-control" id="filter_type" name="filter_type">
                                <option value="">All Types</option>
                                {% for type in filter_types %}
                                    <option value="{{ type }}" {% if current_filters.filter_type == type %}selected{% endif %}>
                                        {{ type|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All</option>
                                <option value="active" {% if current_filters.status == 'active' %}selected{% endif %}>Active</option>
                                <option value="inactive" {% if current_filters.status == 'inactive' %}selected{% endif %}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{% url 'filter_definition_management' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button type="button" class="btn btn-success btn-sm" id="bulk-enable" disabled>
                        <i class="fas fa-check"></i> Enable Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-disable" disabled>
                        <i class="fas fa-times"></i> Disable Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <div>
                    <span class="text-muted">{{ filter_definitions|length }} filter(s) found</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Definitions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if filter_definitions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <th>Name</th>
                                        <th class="d-none d-md-table-cell">Group</th>
                                        <th>Type</th>
                                        <th class="d-none d-lg-table-cell">Features</th>
                                        <th class="d-none d-md-table-cell">Sort Order</th>
                                        <th>Status</th>
                                        <th class="d-none d-lg-table-cell">Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for filter in filter_definitions %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input filter-checkbox" 
                                                       value="{{ filter.id }}">
                                            </td>
                                            <td>
                                                <strong>{{ filter.name }}</strong>
                                                {% if filter.icon %}
                                                    <i class="{{ filter.icon }} text-muted ms-1"></i>
                                                {% endif %}
                                            </td>
                                            <td class="d-none d-md-table-cell">
                                                <span class="badge bg-primary">{{ filter.group.name }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ filter.get_filter_type_display }}</span>
                                            </td>
                                            <td class="d-none d-lg-table-cell">
                                                <div class="d-flex gap-1">
                                                    {% if filter.has_minmax %}
                                                        <span class="badge bg-secondary" title="Has Min/Max">MM</span>
                                                    {% endif %}
                                                    {% if filter.has_enter_value %}
                                                        <span class="badge bg-secondary" title="Has Enter Value">EV</span>
                                                    {% endif %}
                                                    {% if filter.has_search_box %}
                                                        <span class="badge bg-secondary" title="Has Search Box">SB</span>
                                                    {% endif %}
                                                    {% if filter.options %}
                                                        <span class="badge bg-secondary" title="Has Options">{{ filter.options|length }} opts</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td class="d-none d-md-table-cell">{{ filter.sort_order }}</td>
                                            <td>
                                                {% if filter.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td class="d-none d-lg-table-cell">
                                                <small class="text-muted">{{ filter.updated_at|date:"M d, Y" }}</small>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="{% url 'filter_definition_update' filter.pk %}"
                                                       class="btn btn-sm btn-primary action-btn"
                                                       title="Edit Filter" data-bs-toggle="tooltip">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm {% if filter.is_active %}btn-warning{% else %}btn-success{% endif %} action-btn toggle-status"
                                                            data-id="{{ filter.id }}" data-active="{{ filter.is_active }}"
                                                            title="{% if filter.is_active %}Disable Filter{% else %}Enable Filter{% endif %}"
                                                            data-bs-toggle="tooltip">
                                                        {% if filter.is_active %}
                                                            <i class="fas fa-pause"></i>
                                                        {% else %}
                                                            <i class="fas fa-play"></i>
                                                        {% endif %}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger action-btn delete-filter"
                                                            data-id="{{ filter.id }}" data-name="{{ filter.name }}"
                                                            title="Delete Filter" data-bs-toggle="tooltip">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">First</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Previous</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Next</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.group %}&group={{ request.GET.group }}{% endif %}{% if request.GET.filter_type %}&filter_type={{ request.GET.filter_type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Last</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-filter fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No filter definitions found</h5>
                            <p class="text-muted">Create your first filter definition to get started.</p>
                            <a href="{% url 'filter_definition_create' %}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Filter Definition
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the filter "<span id="delete-filter-name"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" id="delete-form" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<style>
/* Enhanced Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-btn i {
    font-size: 0.875rem;
}

/* Enhanced Table Styling */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.001);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Enhanced Cards */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Enhanced Form Controls */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Feature Indicators Enhancement */
.d-flex.gap-1 {
    gap: 0.25rem !important;
}

.d-flex.gap-1 .badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
    }

    .table-responsive {
        border-radius: 8px;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Enhanced Pagination */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #667eea;
    transition: all 0.2s ease;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    transform: translateX(100%);
    transition: all 0.3s ease;
    opacity: 0;
}

.toast-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    font-weight: 500;
}

.toast-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.toast-error {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.toast-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.toast-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
}

.toast-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background-color: rgba(255,255,255,0.2);
}

/* Enhanced Loading States */
.btn.loading {
    position: relative;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Pulse animation for active elements */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
    const bulkButtons = document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete');

    selectAllCheckbox.addEventListener('change', function() {
        filterCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButtons();
    });

    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });

    function updateBulkButtons() {
        const checkedBoxes = document.querySelectorAll('.filter-checkbox:checked');
        const hasSelection = checkedBoxes.length > 0;
        
        bulkButtons.forEach(button => {
            button.disabled = !hasSelection;
        });
    }

    // Handle toggle status
    document.querySelectorAll('.toggle-status').forEach(button => {
        button.addEventListener('click', function() {
            const filterId = this.dataset.id;
            const isActive = this.dataset.active === 'true';
            const originalHTML = this.innerHTML;
            const originalClass = this.className;

            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;
            this.classList.add('loading');

            fetch(`/management/filter-definitions/${filterId}/toggle-status/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success feedback
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    this.classList.remove('loading');
                    this.classList.add('btn-success');

                    // Reload after short delay to show feedback
                    setTimeout(() => {
                        location.reload();
                    }, 800);
                } else {
                    // Restore original state on error
                    this.innerHTML = originalHTML;
                    this.className = originalClass;
                    this.disabled = false;
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                // Restore original state on error
                this.innerHTML = originalHTML;
                this.className = originalClass;
                this.disabled = false;
                alert('Network error occurred. Please try again.');
            });
        });
    });

    // Handle delete
    document.querySelectorAll('.delete-filter').forEach(button => {
        button.addEventListener('click', function() {
            const filterId = this.dataset.id;
            const filterName = this.dataset.name;
            
            document.getElementById('delete-filter-name').textContent = filterName;
            document.getElementById('delete-form').action = `/management/filter-definitions/${filterId}/delete/`;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });

    // Handle bulk actions
    document.getElementById('bulk-enable').addEventListener('click', function() {
        performBulkAction('enable');
    });

    document.getElementById('bulk-disable').addEventListener('click', function() {
        performBulkAction('disable');
    });

    document.getElementById('bulk-delete').addEventListener('click', function() {
        if (confirm('Are you sure you want to delete the selected filters? This action cannot be undone.')) {
            performBulkAction('delete');
        }
    });

    function performBulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.filter-checkbox:checked');
        const filterIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (filterIds.length === 0) {
            showNotification('Please select at least one filter.', 'warning');
            return;
        }

        // Show loading state
        const actionButton = document.getElementById(`bulk-${action}`);
        const originalText = actionButton.innerHTML;
        actionButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        actionButton.disabled = true;

        // Disable other bulk buttons
        document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete').forEach(btn => {
            if (btn !== actionButton) btn.disabled = true;
        });

        fetch('/management/filter-definitions/bulk-toggle/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                filter_ids: filterIds,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success feedback
                actionButton.innerHTML = '<i class="fas fa-check"></i> Success!';
                actionButton.classList.add('btn-success');
                showNotification(data.message, 'success');

                // Reload after delay
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                // Restore button state
                actionButton.innerHTML = originalText;
                actionButton.disabled = false;
                document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete').forEach(btn => {
                    btn.disabled = false;
                });
                showNotification('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            // Restore button state on error
            actionButton.innerHTML = originalText;
            actionButton.disabled = false;
            document.querySelectorAll('#bulk-enable, #bulk-disable, #bulk-delete').forEach(btn => {
                btn.disabled = false;
            });
            showNotification('Network error occurred. Please try again.', 'error');
        });
    }

    // Notification system
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.toast-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification
        const notification = document.createElement('div');
        notification.className = `toast-notification toast-${type}`;
        notification.innerHTML = `
            <div class="toast-content">
                <i class="fas ${getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button type="button" class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }

    function getNotificationIcon(type) {
        switch(type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-exclamation-circle';
            case 'warning': return 'fa-exclamation-triangle';
            default: return 'fa-info-circle';
        }
    }
});
</script>
{% endblock %}
