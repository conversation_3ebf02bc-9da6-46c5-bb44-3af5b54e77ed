{% extends 'base.html' %}

{% block title %}
    {% if object %}Edit Filter Group{% else %}Add Filter Group{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-layer-group"></i>
                        {% if object %}Edit Filter Group{% else %}Add New Filter Group{% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.channel.id_for_label }}" class="form-label">
                                    {{ form.channel.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.channel }}
                                {% if form.channel.help_text %}
                                    <div class="form-text">{{ form.channel.help_text }}</div>
                                {% endif %}
                                {% if form.channel.errors %}
                                    <div class="text-danger">
                                        {% for error in form.channel.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.option_for.id_for_label }}" class="form-label">
                                    {{ form.option_for.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.option_for }}
                                {% if form.option_for.help_text %}
                                    <div class="form-text">{{ form.option_for.help_text }}</div>
                                {% endif %}
                                {% if form.option_for.errors %}
                                    <div class="text-danger">
                                        {% for error in form.option_for.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.sort_order.id_for_label }}" class="form-label">
                                    {{ form.sort_order.label }}
                                </label>
                                {{ form.sort_order }}
                                {% if form.sort_order.help_text %}
                                    <div class="form-text">{{ form.sort_order.help_text }}</div>
                                {% endif %}
                                {% if form.sort_order.errors %}
                                    <div class="text-danger">
                                        {% for error in form.sort_order.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    {{ form.is_active }}
                                    <label for="{{ form.is_active.id_for_label }}" class="form-check-label">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.help_text %}
                                        <div class="form-text">{{ form.is_active.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_active.errors %}
                                        <div class="text-danger">
                                            {% for error in form.is_active.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'filter_group_management' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if object %}Update Filter Group{% else %}Create Filter Group{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if object %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Filters in this Group</h5>
                    </div>
                    <div class="card-body">
                        {% if object.filters.all %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Sort Order</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for filter in object.filters.all %}
                                            <tr>
                                                <td>{{ filter.name }}</td>
                                                <td>
                                                    <span class="badge bg-info">{{ filter.get_filter_type_display }}</span>
                                                </td>
                                                <td>
                                                    {% if filter.is_active %}
                                                        <span class="badge bg-success">Active</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ filter.sort_order }}</td>
                                                <td>
                                                    <a href="{% url 'filter_definition_update' filter.pk %}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No filters in this group yet.</p>
                            <a href="{% url 'filter_definition_create' %}?group={{ object.id }}" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Add First Filter
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
}

.text-danger {
    font-size: 0.875rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 0.375rem;
}
</style>
{% endblock %}
