{% extends 'base.html' %}

{% block title %}Filter Groups{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>Filter Groups</h1>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Name</th>
                <th>Option For</th>
                <th>Channel</th>
                <th>Active</th>
                <th># Filters</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        {% for group in filter_groups %}
            <tr>
                <td>{{ group.name }}</td>
                <td>{{ group.get_option_for_display }}</td>
                <td>{{ group.get_channel_display }}</td>
                <td>{% if group.is_active %}Yes{% else %}No{% endif %}</td>
                <td>{{ group.filters.count }}</td>
                <td>
                    <div class="btn-group" role="group">
                        {% for filter in group.filters.all %}
                            <a href="{% url 'filter-detail' pk=filter.id %}" class="btn btn-sm btn-outline-primary" title="{{ filter.name }}">
                                {{ filter.name|truncatechars:10 }}
                            </a>
                        {% empty %}
                            <span class="text-muted">No filters</span>
                        {% endfor %}
                    </div>
                </td>
            </tr>
        {% empty %}
            <tr>
                <td colspan="6">No filter groups found.</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
