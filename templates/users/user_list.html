{% extends 'base.html' %}

{% block title %}Users - CreatorVerse{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
/* Modern User Management Styling */
.badge-brand {
    background: linear-gradient(135deg, #FFC107 0%, #FF8F00 100%) !important;
    color: #212529;
    font-weight: 500;
}

.badge-influencer {
    background: linear-gradient(135deg, #17A2B8 0%, #0D7377 100%) !important;
    color: white;
    font-weight: 500;
}

.table-users {
    font-size: 0.95rem;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.avatar-sm:hover {
    border-color: #667eea;
    transform: scale(1.1);
}

.inactive-row {
    background-color: rgba(0,0,0,0.03);
    opacity: 0.7;
}

/* Enhanced Table Styling */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.001);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
}

/* Enhanced Cards */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: #495057;
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.badge.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* Enhanced Search */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
    padding: 1.5rem 0;
}

.page-title {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0;
    font-size: 2rem;
}

.page-title i {
    color: #667eea;
    margin-right: 0.75rem;
}

/* User Avatar Placeholder */
.avatar-placeholder {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

/* Enhanced Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-btn i {
    font-size: 0.875rem;
}

/* Enhanced Breadcrumbs */
.modern-breadcrumb {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.modern-breadcrumb .breadcrumb-item {
    font-weight: 500;
}

.modern-breadcrumb .breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.2s ease;
}

.modern-breadcrumb .breadcrumb-item a:hover {
    color: #764ba2;
    transform: translateX(2px);
}

.modern-breadcrumb .breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

.modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    .table th, .table td {
        padding: 0.75rem 0.5rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .input-group {
        width: 100% !important;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
    }

    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb modern-breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fas fa-home"></i> Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'user_dashboard_home' %}"><i class="fas fa-users"></i> User Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-user-friends"></i> User Management
            </li>
        </ol>
    </nav>

    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-users"></i> User Management
                </h1>
                <p class="text-muted">Manage and monitor all users in the system</p>
            </div>
            <div>
                <a href="{% url 'user_dashboard_home' %}" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar"></i> Analytics Dashboard
                </a>
            </div>
        </div>
    </div>

  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">All Users</h5>
        <div class="input-group w-50">
            <input type="text" id="userSearch" class="form-control" placeholder="Search users...">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-users">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Type</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr class="{% if not user.is_active %}inactive-row{% endif %}">
                        <td>
                            <div class="d-flex align-items-center">
                                {% if user.profile_image %}
                                <img src="{{ user.profile_image }}" alt="Avatar" class="avatar-sm me-2">
                                {% else %}
                                <div class="avatar-sm me-2 avatar-placeholder">
                                    {{ user.name|default:"U"|first|upper }}
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ user.name|default:"Unnamed User" }}</strong>
                                    <div class="text-muted small">
                                        {{ user.id|truncatechars:8 }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if user.user_type == 'brand' %}
                            <span class="badge badge-brand">Brand</span>
                            {% elif user.user_type == 'influencer' %}
                            <span class="badge badge-influencer">Influencer</span>
                            {% else %}
                            <span class="badge bg-secondary">Unknown</span>
                            {% endif %}
                        </td>
                        <td>{{ user.email }}</td>
                        <td>
                            <span class="badge {% if user.status == 'active' %}bg-success{% elif user.status == 'requested' %}bg-warning{% else %}bg-secondary{% endif %}">
                                {{ user.status|title }}
                            </span>
                        </td>
                        <td>{{ user.created_at|date:"M j, Y" }}</td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'user_detail' user.id %}"
                                   class="btn btn-sm btn-primary action-btn"
                                   title="View User Details" data-bs-toggle="tooltip">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button"
                                        class="btn btn-sm btn-info action-btn"
                                        title="Edit User" data-bs-toggle="tooltip">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button"
                                        class="btn btn-sm {% if user.is_active %}btn-warning{% else %}btn-success{% endif %} action-btn"
                                        title="{% if user.is_active %}Deactivate User{% else %}Activate User{% endif %}"
                                        data-bs-toggle="tooltip">
                                    {% if user.is_active %}
                                        <i class="fas fa-user-slash"></i>
                                    {% else %}
                                        <i class="fas fa-user-check"></i>
                                    {% endif %}
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <p class="mb-0">No users found</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
  </div><!-- end fade-in card -->
</div><!-- end fade-in -->
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Enhanced client-side search with debouncing
    const userSearch = document.getElementById('userSearch');
    let searchTimeout;

    userSearch.addEventListener('keyup', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            const searchText = userSearch.value.toLowerCase();
            const rows = document.querySelectorAll('.table-users tbody tr');
            let visibleCount = 0;

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if(text.includes(searchText)) {
                    row.style.display = '';
                    row.style.animation = 'fadeIn 0.3s ease';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Show/hide no results message
            const noResultsRow = document.querySelector('.no-results-row');
            if (visibleCount === 0 && searchText.length > 0) {
                if (!noResultsRow) {
                    const tbody = document.querySelector('.table-users tbody');
                    const newRow = document.createElement('tr');
                    newRow.className = 'no-results-row';
                    newRow.innerHTML = `
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-search fa-2x text-muted mb-2"></i>
                            <p class="mb-0">No users found matching "${searchText}"</p>
                        </td>
                    `;
                    tbody.appendChild(newRow);
                }
            } else if (noResultsRow) {
                noResultsRow.remove();
            }
        }, 300);
    });

    // Add fade-in animation for table rows
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
