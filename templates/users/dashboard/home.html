{% extends 'base.html' %}

{% block title %}User Dashboard - CreatorVerse{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
<style>
/* Enhanced Dashboard Styling */
.stat-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-chart {
    height: 300px;
}

/* Enhanced Cards */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Enhanced List Groups */
.list-group-item {
    border-left: none;
    border-right: none;
    border-radius: 0;
    transition: all 0.2s ease;
    padding: 1rem 1.5rem;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.list-group-item:first-child {
    border-top: none;
    border-radius: 0;
}

.list-group-item:last-child {
    border-bottom: none;
    border-radius: 0;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.badge.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

/* Enhanced Breadcrumbs */
.modern-breadcrumb {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.modern-breadcrumb .breadcrumb-item {
    font-weight: 500;
}

.modern-breadcrumb .breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.2s ease;
}

.modern-breadcrumb .breadcrumb-item a:hover {
    color: #764ba2;
    transform: translateX(2px);
}

.modern-breadcrumb .breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

.modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: 600;
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
}

.page-title i {
    color: #667eea;
    margin-right: 0.75rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 0;
}

/* Quick Links Enhancement */
.quicklink-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quicklink-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.quicklink-card .card-body {
    text-align: center;
    padding: 2rem 1.5rem;
}

.quicklink-card .card-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.quicklink-card .card-text {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.8rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }

    .dashboard-chart {
        height: 250px;
    }
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card, .card {
    animation: fadeInUp 0.6s ease forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb modern-breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fas fa-home"></i> Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-users"></i> User Dashboard
            </li>
        </ol>
    </nav>

    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-bar"></i> CreatorVerse Analytics Dashboard
        </h1>
        <p class="page-subtitle">Monitor user activity, engagement, and system performance</p>
    </div>

    <!-- Navigation Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group w-100" role="group">
                        <a href="{% url 'user_analytics' %}" class="btn btn-outline-primary">
                            <i class="fas fa-users"></i> Users
                        </a>
                        <a href="{% url 'org_analytics' %}" class="btn btn-outline-primary">
                            <i class="fas fa-building"></i> Organizations
                        </a>
                        <a href="{% url 'brand_analytics' %}" class="btn btn-outline-primary">
                            <i class="fas fa-tags"></i> Brands
                        </a>
                        <a href="{% url 'auth_analytics' %}" class="btn btn-outline-primary">
                            <i class="fas fa-shield-alt"></i> Authentication
                        </a>
                        <a href="{% url 'session_analytics' %}" class="btn btn-outline-primary">
                            <i class="fas fa-clock"></i> Sessions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="row">
    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-gradient-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-users"></i> Total Users
                        </h5>
                        <div class="stat-number">{{ total_users }}</div>
                        <div class="d-flex justify-content-between">
                            <small class="opacity-75">Active: {{ active_users }}</small>
                            <small class="opacity-75">Recent: +{{ recent_users }}</small>
                        </div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 4px;">
                    <div class="progress-bar bg-white opacity-50" style="width: {% widthratio active_users total_users 100 %}%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-gradient-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-building"></i> Organizations
                        </h5>
                        <div class="stat-number">{{ total_orgs }}</div>
                        <small class="opacity-75">Active organizations</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-building fa-2x opacity-75"></i>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 4px;">
                    <div class="progress-bar bg-white opacity-50" style="width: 85%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-gradient-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-tags"></i> Brands
                        </h5>
                        <div class="stat-number">{{ total_brands }}</div>
                        <small class="opacity-75">Registered brands</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-tags fa-2x opacity-75"></i>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 4px;">
                    <div class="progress-bar bg-white opacity-50" style="width: 70%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-gradient-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-clock"></i> Active Sessions
                        </h5>
                        <div class="stat-number">{{ active_sessions }}</div>
                        <small class="opacity-75">Current sessions</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 4px;">
                    <div class="progress-bar bg-white opacity-50" style="width: 60%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">User Status Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="userStatusChart" class="dashboard-chart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">User Verification</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Email Verified
                        <span class="badge bg-success rounded-pill">{{ email_verified }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Phone Verified
                        <span class="badge bg-info rounded-pill">{{ phone_verified }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        New Users (7 days)
                        <span class="badge bg-primary rounded-pill">{{ recent_users }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Active Sessions
                        <span class="badge bg-warning rounded-pill">{{ active_sessions }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quicklinks</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card quicklink-card">
                            <div class="card-body">
                                <div class="mb-3">
                                    <i class="fas fa-users fa-3x text-primary"></i>
                                </div>
                                <h5 class="card-title">User Management</h5>
                                <p class="card-text">View and manage all users in the system.</p>
                                <a href="{% url 'user_list' %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Go to Users
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card quicklink-card">
                            <div class="card-body">
                                <div class="mb-3">
                                    <i class="fas fa-building fa-3x text-success"></i>
                                </div>
                                <h5 class="card-title">Organization Management</h5>
                                <p class="card-text">Manage organizations and memberships.</p>
                                <a href="{% url 'organization_list' %}" class="btn btn-success">
                                    <i class="fas fa-arrow-right"></i> Go to Organizations
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card quicklink-card">
                            <div class="card-body">
                                <div class="mb-3">
                                    <i class="fas fa-tags fa-3x text-warning"></i>
                                </div>
                                <h5 class="card-title">Brand Management</h5>
                                <p class="card-text">Manage brands and their members.</p>
                                <a href="{% url 'brand_list' %}" class="btn btn-warning">
                                    <i class="fas fa-arrow-right"></i> Go to Brands
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card quicklink-card">
                            <div class="card-body">
                                <div class="mb-3">
                                    <i class="fas fa-filter fa-3x text-info"></i>
                                </div>
                                <h5 class="card-title">Filter Management</h5>
                                <p class="card-text">Manage creator filters and categories.</p>
                                <a href="{% url 'filter_management_dashboard' %}" class="btn btn-info">
                                    <i class="fas fa-arrow-right"></i> Go to Filters
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // User status distribution chart using actual data from context
    var userStatusCtx = document.getElementById('userStatusChart').getContext('2d');
    var statusData = {{ user_status_counts|safe }};
    
    // Extract labels and data from status_counts
    var labels = statusData.map(item => item.status || 'Unknown');
    var data = statusData.map(item => item.count || 0);
    var colors = ['#28a745', '#17a2b8', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14'];
    
    var userStatusChart = new Chart(userStatusCtx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + ' users';
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}