/* Responsive Design Enhancements */

/* Mobile-first approach */
@media (max-width: 576px) {
    /* Navigation */
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .brand-text, .brand-subtitle {
        display: none;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 0.75rem !important;
        margin: 0.125rem 0;
    }
    
    /* Tables */
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .table th, .table td {
        padding: 0.5rem 0.25rem;
        white-space: nowrap;
    }
    
    /* Hide less important columns on mobile */
    .table .d-none-mobile {
        display: none !important;
    }
    
    /* Action buttons stack vertically */
    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }
    
    .action-btn {
        width: 100%;
        height: 28px;
        font-size: 0.75rem;
    }
    
    /* Cards */
    .card-body {
        padding: 0.75rem;
    }
    
    .card-header {
        padding: 0.75rem 1rem;
    }
    
    /* Statistics cards */
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    /* Forms */
    .form-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .section-title {
        font-size: 1rem;
        flex-direction: column;
        text-align: center;
    }
    
    .form-control, .form-select {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
    
    /* Buttons */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .page-actions {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }
    
    .page-actions .btn {
        width: 100%;
    }
    
    /* Bulk actions */
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .d-flex.justify-content-between > div:first-child {
        order: 2;
    }
    
    .d-flex.justify-content-between > div:last-child {
        order: 1;
        text-align: center;
    }
    
    /* Pagination */
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .pagination .page-item {
        margin: 0.125rem;
    }
    
    /* Breadcrumbs */
    .modern-breadcrumb {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    /* Toast notifications */
    .toast-notification {
        left: 10px;
        right: 10px;
        top: 10px;
        min-width: auto;
        max-width: none;
    }
}

/* Tablet styles */
@media (min-width: 577px) and (max-width: 768px) {
    /* Tables */
    .table th, .table td {
        padding: 0.75rem 0.5rem;
    }
    
    /* Action buttons */
    .action-buttons {
        gap: 0.25rem;
    }
    
    .action-btn {
        width: 30px;
        height: 30px;
    }
    
    /* Cards */
    .stat-card .card-body {
        padding: 1.25rem;
    }
    
    /* Forms */
    .form-section {
        padding: 1.25rem;
    }
    
    /* Page header */
    .page-header-with-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* Desktop styles */
@media (min-width: 992px) {
    /* Enhanced hover effects for desktop */
    .table tbody tr:hover {
        transform: scale(1.002);
    }
    
    .card:hover {
        transform: translateY(-4px);
    }
    
    .stat-card:hover {
        transform: translateY(-6px);
    }
    
    /* Better spacing for larger screens */
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    .form-section {
        padding: 2rem;
    }
    
    .card-body {
        padding: 2rem;
    }
}

/* Large desktop styles */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    .stat-card h3 {
        font-size: 2.5rem;
    }
}

/* Print styles */
@media print {
    .navbar,
    .page-actions,
    .action-buttons,
    .btn,
    .toast-notification {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .page-header-with-actions {
        margin-bottom: 1rem;
    }
    
    .modern-breadcrumb {
        background: none;
        border: 1px solid #dee2e6;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
    
    .form-control, .form-select {
        border: 2px solid #000;
    }
    
    .badge {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .card:hover,
    .btn:hover,
    .action-btn:hover {
        transform: none;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    /* This can be expanded later for dark mode support */
    .form-section {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .section-title {
        color: #e2e8f0;
    }
    
    .section-description {
        color: #a0aec0;
    }
}

/* Focus management for accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus,
.form-check-input:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Skip to content link for accessibility */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #667eea;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-to-content:focus {
    top: 6px;
}

/* Loading states for slow connections */
@media (max-width: 768px) {
    .loading::after {
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
    }
}

/* Utility classes for responsive design */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

@media (max-width: 768px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
    
    .mobile-stack {
        flex-direction: column !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
}
