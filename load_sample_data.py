#!/usr/bin/env python3
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'creatorverse_filters.settings')
django.setup()

from filter_catalog.models import FilterGroup, FilterDefinition, LocationHierarchy

def load_sample_data():
    print("Loading sample data...")
    
    # Check if data already exists
    if FilterGroup.objects.exists():
        print("Data already exists, skipping...")
        return
    
    # Create filter groups for Instagram creators
    print("Creating filter groups...")
    demography_group = FilterGroup.objects.create(
        name='Demography & Identity',
        option_for='creator',
        channel='instagram',
        sort_order=1
    )
    
    performance_group = FilterGroup.objects.create(
        name='Performance Metrics',
        option_for='creator',
        channel='instagram',
        sort_order=2
    )
    
    content_group = FilterGroup.objects.create(
        name='Content & Niche',
        option_for='creator',
        channel='instagram',
        sort_order=3
    )
    
    # Create some filters
    print("Creating filter definitions...")
    
    # Gender filter
    gender_filter = FilterDefinition.objects.create(
        group=demography_group,
        name='Gender',
        filter_type='radio-button',
        icon='gender-icon',
        has_minmax=False,
        has_enter_value=False,
        placeholder='Select Gender',
        sort_order=1,
        db_field='gender',
        api_field='gender',
        options=[
            {'label': 'Male', 'value': 'male', 'description': 'Male creators'},
            {'label': 'Female', 'value': 'female', 'description': 'Female creators'},
            {'label': 'Other', 'value': 'other', 'description': 'Non-binary creators'}
        ]
    )
    
    # Age filter
    age_filter = FilterDefinition.objects.create(
        group=demography_group,
        name='Age',
        filter_type='checkbox',
        icon='age-icon',
        has_minmax=True,
        has_enter_value=False,
        placeholder='Select Age',
        sort_order=2,
        db_field='age_range',
        api_field='age_range',
        options=[
            {'label': 'Teen', 'value': '13-19', 'description': '13-19'},
            {'label': 'Young Adult', 'value': '20-35', 'description': '20-35'},
            {'label': 'Adult', 'value': '36-55', 'description': '36-55'},
            {'label': 'Senior', 'value': '56+', 'description': '56+'}
        ]
    )
    
    # Location filter
    location_filter = FilterDefinition.objects.create(
        group=demography_group,
        name='Location',
        filter_type='checkbox',
        icon='location-icon',
        has_minmax=False,
        has_enter_value=True,
        placeholder='Enter Location',
        sort_order=3,
        db_field='location_country',
        api_field='location'
    )
    
    # Follower count filter
    follower_filter = FilterDefinition.objects.create(
        group=performance_group,
        name='Follower Count',
        filter_type='checkbox',
        icon='follower-icon',
        has_minmax=True,
        has_enter_value=False,
        placeholder='Select Follower Count',
        sort_order=1,
        db_field='follower_count',
        api_field='followers',
        options=[
            {'label': 'Nano (1K-10K)', 'value': '1000-10000', 'description': '1,000-10,000 followers'},
            {'label': 'Micro (10K-50K)', 'value': '10000-50000', 'description': '10,000-50,000 followers'},
            {'label': 'Mid-Tier (50K-500K)', 'value': '50000-500000', 'description': '50,000-500,000 followers'},
            {'label': 'Macro (500K-1M)', 'value': '500000-1000000', 'description': '500,000-1,000,000 followers'},
            {'label': 'Mega (1M+)', 'value': '1000000-', 'description': 'Over 1,000,000 followers'}
        ]
    )
    
    # Category filter
    category_filter = FilterDefinition.objects.create(
        group=content_group,
        name='Category',
        filter_type='checkbox',
        icon='category-icon',
        has_minmax=False,
        has_enter_value=False,
        placeholder='Select Category',
        sort_order=1,
        db_field='categories',
        api_field='category',
        options=[
            {'label': 'Fashion', 'value': 'fashion', 'description': 'Fashion and style'},
            {'label': 'Beauty', 'value': 'beauty', 'description': 'Beauty and makeup'},
            {'label': 'Lifestyle', 'value': 'lifestyle', 'description': 'Lifestyle content'},
            {'label': 'Travel', 'value': 'travel', 'description': 'Travel and tourism'},
            {'label': 'Food', 'value': 'food', 'description': 'Food and cooking'},
            {'label': 'Fitness', 'value': 'fitness', 'description': 'Fitness and health'},
            {'label': 'Tech', 'value': 'tech', 'description': 'Technology and gadgets'},
            {'label': 'Gaming', 'value': 'gaming', 'description': 'Gaming and esports'}
        ]
    )
    
    # Create some location data
    print("Creating location hierarchy...")
    
    # Add countries
    usa = LocationHierarchy.objects.create(
        name='United States',
        code='US',
        level=0,
        tier='tier1'
    )
    
    india = LocationHierarchy.objects.create(
        name='India',
        code='IN',
        level=0,
        tier='tier1'
    )
    
    # Add states/regions
    california = LocationHierarchy.objects.create(
        name='California',
        code='CA',
        level=1,
        parent=usa,
        tier='tier1'
    )
    
    maharashtra = LocationHierarchy.objects.create(
        name='Maharashtra',
        code='MH',
        level=1,
        parent=india,
        tier='tier1'
    )
    
    # Add cities
    LocationHierarchy.objects.create(
        name='Los Angeles',
        level=2,
        parent=california,
        tier='tier1',
        population=3980000
    )
    
    LocationHierarchy.objects.create(
        name='Mumbai',
        level=2,
        parent=maharashtra,
        tier='tier1',
        population=20400000
    )
    
    print("Successfully loaded sample data!")

if __name__ == '__main__':
    load_sample_data()
